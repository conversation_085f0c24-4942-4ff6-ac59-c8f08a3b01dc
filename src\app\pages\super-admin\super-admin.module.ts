import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';

import { SuperAdminRoutingModule } from './super-admin-routing.module';
import { SuperAdminComponent } from './super-admin.component';
import { SuperAdminDashboardModule } from './super-admin-dashboard/super-admin-dashboard.module';
import { SettingsComponent } from './settings/settings.component';
import { AdminCardComponent } from './settings/components/admin-card/admin-card.component';
import { SubscriptionsComponent } from './settings/packages/packages.component';
import { SubscriptionFormComponent } from './settings/subscription-form/subscription-form.component';
import { UserSettingsComponent } from './settings/user-settings/user-settings.component';

// User Account Types Components
import { UserAccountTypesComponent } from './settings/user-account-types/user-account-types.component';
import { AccountManagementCardComponent } from './settings/user-account-types/components/account-management-card/account-management-card.component';
import { UsersComponent } from './settings/user-account-types/users/users.component';
import { PermissionsComponent } from './settings/user-account-types/permissions/permissions.component';
import { RolesComponent } from './settings/user-account-types/roles/roles.component';

// Table Components
import { AllDevelopersComponent } from './all-developers/all-developers.component';
import { DeveloperDetailsComponent } from './all-developers/developer-details/developer-details.component';
import { AllBrokersComponent } from './all-brokers/all-brokers.component';
import { BrokerDetailsComponent } from './all-brokers/broker-details/broker-details.component';
import { AllUsersComponent } from './all-users/all-users.component';

// Pagination Component
import { PaginationComponent } from '../../pagination/pagination.component';

@NgModule({
  declarations: [
    SuperAdminComponent,
    SettingsComponent,
    AdminCardComponent,
    SubscriptionsComponent,
    UserSettingsComponent,
    SubscriptionFormComponent,
    // User Account Types Components
    UserAccountTypesComponent,
    AccountManagementCardComponent,
    UsersComponent,
    PermissionsComponent,
    RolesComponent,
    // Table Components
    AllDevelopersComponent,
    DeveloperDetailsComponent,
    AllBrokersComponent,
    BrokerDetailsComponent,
    AllUsersComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    InlineSVGModule,
    SuperAdminRoutingModule,
    SuperAdminDashboardModule,
    PaginationComponent
  ]
})
export class SuperAdminModule { }
