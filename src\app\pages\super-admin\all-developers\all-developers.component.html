<!-- Main Card with Projects Design -->
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">All Developers</h1>
          </div>
          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <i class="fas fa-search fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"></i>
              <input type="text" name="searchText" [(ngModel)]="searchText" (ngModelChange)="onSearchChange($event)"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                placeholder="Search By Developer Name.." data-kt-search-element="input" />
            </form>
          </div>
          <div class="d-flex my-4">
            <div class="position-relative me-3">
              <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
                <i class="fa-solid fa-filter"></i> Filter
              </a>
            </div>

            <a class="btn btn-sm btn-dark-blue cursor-pointer">
              <i class="fa-solid fa-plus"></i>
              Add Developer
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="table-responsive mb-5">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px">Developer</th>
            <th class="min-w-140px">Email</th>
            <th class="min-w-120px">Phone</th>
            <th class="min-w-150px">No Of projects</th>
            <th class="min-w-100px">Projects</th>
            <th class="min-w-100px">Active</th>
            <th class="min-w-100px text-end rounded-end pe-4">Actions</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let developer of developers">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol-label">
                  <img *ngIf="developer.image" [src]="developer.image" alt="" class="w-100 h-100"
                    style="object-fit: cover; border-radius: 50%" />
                  <div *ngIf="!developer.image"
                    class="bg-light-primary text-primary fw-bold fs-6 w-100 h-100 d-flex align-items-center justify-content-center"
                    style="border-radius: 50%">
                    {{ developer.fullName.charAt(0) }}
                  </div>
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ developer?.fullName }}
                  </a>
                  <span class="text-muted fs-7">{{ developer.specialization }}</span>
                </div>
              </div>
            </td>

            <td>
              <div class="d-flex flex-column">
                <span class="text-gray-800 fw-semibold fs-7 mb-1">{{ developer.email }}</span>
                <span class="text-muted fw-semibold fs-7 d-lg-none">{{ developer.phone }}</span>
              </div>
            </td>

            <td class="d-none d-lg-table-cell">
              <span class="text-gray-800 fw-semibold fs-6">{{ developer.phone }}</span>
            </td>

            <td class="d-none d-xl-table-cell">
              <span class="badge badge-light-primary fs-7 fw-bold">{{ developer.numberOfProjects }}</span>
            </td>

            <td class="d-none d-lg-table-cell">
              <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: developer.developerId }"
                class="badge badge-light-info fs-7 fw-bold">
                Projects
              </a>
            </td>

            <td>
              <span class="badge fs-7 fw-bold px-3 py-2" [ngClass]="getStatusClass(developer.isActive)">
                {{ developer.isActive }}
              </span>
            </td>

            <td class="text-end pe-4">
              <button class="btn btn-sm btn-icon btn-light-primary" data-bs-toggle="tooltip" title="View Details"
                (click)="viewDeveloper(developer)">
                <i class="fas fa-eye fs-6"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="!loading && developers.length > 0" class="d-flex justify-content-center mt-5 mb-5">
      <app-pagination [totalItems]="totalElements" [itemsPerPage]="pageSize" [currentPage]="currentPage"
        (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
</div>